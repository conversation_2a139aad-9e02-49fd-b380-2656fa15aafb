import React, { Suspense } from 'react';
import { usePathname } from 'next/navigation';

import styled from '@benzinga/themetron';
import { Button } from '@benzinga/core-ui';

import { TradeIdeasWidget } from '@benzinga/trade-ideas';

import type { CallToActionFormProps } from '@benzinga/forms-ui';
import { PodcastsListBlock } from '@benzinga/blocks';
import { MoneyWidget } from '@benzinga/money';
import { BenzingaBriefs } from '@benzinga/news';

import { StoryObject } from '@benzinga/basic-news-manager';
import { Podcast, WordpressWidget } from '@benzinga/content-manager';
import { TradeIdea } from '@benzinga/trade-ideas-manager';
import { Watchlist } from '@benzinga/watchlist-manager';

import { HomePageTabKey } from '../interface';

const CallToActionForm = React.lazy(() =>
  import('@benzinga/forms-ui').then(module => ({
    default: module.CallToActionForm,
  })),
);

const RaptiveAdPlaceholder = React.lazy(() =>
  import('@benzinga/ads').then(module => {
    return { default: module.RaptiveAdPlaceholder };
  }),
);

// const GoogleAdUnit = React.lazy(() =>
//   import('@benzinga/ads').then(module => ({
//     default: module.GoogleAdUnit,
//   })),
// );

// const Primis = React.lazy(() =>
//   import('@benzinga/ads').then(module => {
//     return { default: module.Primis };
//   }),
// );

const WatchlistSidebarWidget = React.lazy(() =>
  import('@benzinga/widgets').then(module => {
    return { default: module.WatchlistSidebarWidget };
  }),
);

export interface HomePageSidebarProps {
  adUnitPrimary?: React.ReactElement;
  adUnitSecondary?: React.ReactElement;
  briefs?: StoryObject[];
  isLoading?: boolean;
  newsletter?: CallToActionFormProps;
  podcasts?: Podcast[];
  sidebarArticles?: WordpressWidget;
  brokerWidget?: WordpressWidget;
  tradeIdeas?: TradeIdea[];
  watchlists?: Watchlist[];
  activeTab?: HomePageTabKey;
}

export const HomePageSidebar = (props: HomePageSidebarProps) => {
  const pathname = usePathname();

  return (
    <div className="sidebar-wrapper">
      {props.adUnitPrimary}

      <WatchlistSidebarWidget />

      {/* <AdWrapper>
        <LazyLoad height={220} offset={200} once>
          <Primis id="111717" />
        </LazyLoad>
      </AdWrapper> */}

      <Suspense fallback={<div />}>
        <CallToActionForm {...props.newsletter} />
      </Suspense>

      {pathname !== '/briefs' && (
        <div>
          <React.Suspense>
            <BenzingaBriefs initialBriefs={props.briefs} />
          </React.Suspense>
          <a
            className="benzinga-briefs-footer"
            data-action="Benzinga Briefs See More Click"
            href="https://www.benzinga.com/benzinga-briefs"
          >
            <Button className="justify-center w-full" variant="primary">
              <span>See All</span>
            </Button>
          </a>
        </div>
      )}

      {props.sidebarArticles && <MoneyWidget widget={props.sidebarArticles} />}

      {/* <div style={{ height: '250px', margin: 'auto', marginTop: '16px', textAlign: 'center', width: '300px' }}>
        <React.Suspense>
          <GoogleAdUnit
            adUnit="Benzinga:Right-Sidebar:300x250:Middle"
            dfpNetworkId="4107070"
            sizes={[[300, 250]]}
            slotId="home-sidebar-middle"
          />
        </React.Suspense>
      </div> */}

      <RaptiveAdPlaceholder className="w-[300px] mb-2 overflow-hidden" type="static-sidebar" />

      {props.podcasts?.length ? <PodcastsListBlock podcasts={props.podcasts} /> : null}

      {props.tradeIdeas ? <TradeIdeasWidget items={props.tradeIdeas} title="ZINGERNATION Trade Ideas" /> : null}

      {/* <div style={{ height: '250px', margin: 'auto', marginTop: '16px', textAlign: 'center', width: '300px' }}>
        <GoogleAdUnit
          adUnit="Benzinga:300x250-Right-Bottom"
          dfpNetworkId="4107070"
          sizes={[[300, 250]]}
          slotId="home-sidebar-bottom"
        />
      </div> */}

      <RaptiveAdPlaceholder className="w-[300px] mb-2 overflow-hidden" type="sticky-sidebar" />
    </div>
  );
};

// const AdWrapper = styled.div`
//   .primis-video {
//     width: 300px;
//     #primis_container_div {
//       z-index: 1 !important;
//     }
//   }
// `;
