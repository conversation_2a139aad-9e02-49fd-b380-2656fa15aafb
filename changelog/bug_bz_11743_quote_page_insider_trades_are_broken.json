{"affectedProjects": ["bz", "bz-mobile", "india", "money", "pro", "proto", "@benzinga/root", "amp", "bz-changelog", "bz-e2e", "data-iam", "data-manager-advanced-news", "data-manager-alts", "data-manager-article", "data-manager-autocomplete", "data-manager-basic-news", "data-manager-benzinga-gpt", "data-manager-calendar", "data-manager-calendars-commons", "data-manager-calendars-conference-calls", "data-manager-calendars-dividends", "data-manager-calendars-earnings", "data-manager-calendars-economics", "data-manager-calendars-fda", "data-manager-calendars-government-trades", "data-manager-calendars-guidance", "data-manager-calendars-ipos", "data-manager-calendars-ma", "data-manager-calendars-offerings", "data-manager-calendars-option-activity", "data-manager-calendars-ratings", "data-manager-calendars-retail", "data-manager-calendars-sec", "data-manager-calendars-short-interest", "data-manager-calendars-splits", "data-manager-calendars-squawk", "data-manager-chart", "data-manager-chart-config", "data-manager-chat", "data-manager-commodities", "data-manager-content", "data-manager-crypto", "data-manager-etfs", "data-manager-events", "data-manager-forex-manager", "data-manager-fund", "data-manager-geo", "data-manager-gov-trades", "data-manager-heatmap", "data-manager-holdings", "data-manager-iap", "data-manager-insider-trades", "data-manager-internal-news", "data-manager-layout", "data-manager-legacy-scanner", "data-manager-livestream", "data-manager-logo-analytics", "data-manager-marketing-wins", "data-manager-mortgage-rates", "data-manager-movers", "data-manager-news-alerts", "data-manager-notes", "data-manager-notification", "data-manager-onboarding", "data-manager-permission", "data-manager-plus", "data-manager-price-alerts", "data-manager-product", "data-manager-quote-holdings", "data-manager-quotes", "data-manager-scanner", "data-manager-scanner-config", "data-manager-securities", "data-manager-shop", "data-manager-signals", "data-manager-squawk", "data-manager-stock-reports", "data-manager-subscription", "data-manager-text-to-speech", "data-manager-time", "data-manager-token", "data-manager-top-stocks-manager", "data-manager-trade-ideas", "data-manager-trending-topics", "data-manager-user", "data-manager-videos", "data-manager-watchlists", "data-manager-watchlists-holdings", "data-manager-widget-linking", "data-manager-wiims", "data-manager-zing-rewards", "data-managers-examples", "data-session", "data-squawk-sdk", "google-sheets-db", "icons-fontawesome-custom", "icons-icons", "icons-v<PERSON><PERSON><PERSON>", "iter", "legacy-fission", "news-user-settings", "newsdesk-tools", "price-alert", "pro-e2e", "pro-user-settings", "react-utils-data-hooks-calendar-manager", "react-utils-data-hooks-chart-manager", "react-utils-data-hooks-content-manager", "react-utils-data-hooks-crypto-manager", "react-utils-data-hooks-fund-manager", "react-utils-data-hooks-livestream-manager", "react-utils-data-hooks-news-manager", "react-utils-data-hooks-quotes-manager", "react-utils-data-hooks-scanner-manager", "react-utils-data-hooks-securities-manager", "react-utils-data-hooks-time-manager", "react-utils-data-hooks-watchlist-manager", "react-utils-hooks", "react-utils-pro-tools", "react-utils-session-context", "react-utils-themetron", "react-utils-user-context", "react-utils-widget-tools", "search-modules", "styles-global-styles", "third-party-chartiq", "tsmd", "ui-ads", "ui-ads-utils", "ui-ag-grid-utils", "ui-alternative-investments", "ui-article", "ui-atomics", "ui-auth", "ui-blocks", "ui-bz-onboarding", "ui-calculators", "ui-calendars", "ui-charts", "ui-comments", "ui-core", "ui-crypto", "ui-edge", "ui-entity", "ui-etfs", "ui-filter", "ui-forms", "ui-image", "ui-logos", "ui-miller-columns", "ui-money", "ui-navigation", "ui-news", "ui-next-utils", "ui-pro-ui", "ui-product", "ui-quotes", "ui-reviews", "ui-search", "ui-styles", "ui-table", "ui-templates", "ui-testing", "ui-ticker", "ui-trade-ideas", "ui-translate", "ui-ui", "ui-user", "ui-watchlist-ui", "ui-widgets", "user-settings", "utils-analytics", "utils-auth", "utils-blocks", "utils-containers", "utils-date", "utils-device", "utils-frontend", "utils-manager-desktop-notification", "utils-manager-play-sound", "utils-manager-tracking", "utils-monitoring-coralogix", "utils-monitoring-datadog", "utils-safe-await", "utils-seo", "utils-subscribable", "utils-utils", "visualization-analyst-ratings", "visualization-bar-chart", "visualization-guage", "visualization-heatmap", "visualization-iqchart", "visualization-pie-chart", "visualization-plotly", "visualization-sank<PERSON>", "visualization-trading-view-charting-libarary", "visualization-trading-view-light-weight-chart", "visualization-visualization-utils", "widget-chartwidget", "widget-partner-widget", "widget-pro-bz-chart", "widget-pro-calendars", "widget-pro-chart", "widget-pro-chat", "widget-pro-details", "widget-pro-gpt", "widget-pro-graph", "widget-pro-home", "widget-pro-insiders", "widget-pro-movers", "widget-pro-newsfeed", "widget-pro-notification", "widget-pro-research", "widget-pro-scanner", "widget-pro-signals", "widget-pro-watchlist", "widget-pro-widget-utils", "widget-scanner", "widget-sensa-market", "widget-ticker-finder", "widget-utils", "widgets-pro-calendar", "widgets-pro-insiders"], "description": "quote page insider trades are broken", "epic": null, "issueNumber": "11743", "project": "BZ", "projects": ["bz-mobile", "money", "proto", "@benzinga/root", "amp", "bz-changelog", "bz-e2e", "data-iam", "data-manager-advanced-news", "data-manager-alts", "data-manager-article", "data-manager-autocomplete", "data-manager-basic-news", "data-manager-benzinga-gpt", "data-manager-calendar", "data-manager-calendars-commons", "data-manager-calendars-conference-calls", "data-manager-calendars-dividends", "data-manager-calendars-earnings", "data-manager-calendars-economics", "data-manager-calendars-fda", "data-manager-calendars-government-trades", "data-manager-calendars-guidance", "data-manager-calendars-ipos", "data-manager-calendars-ma", "data-manager-calendars-offerings", "data-manager-calendars-option-activity", "data-manager-calendars-ratings", "data-manager-calendars-retail", "data-manager-calendars-sec", "data-manager-calendars-short-interest", "data-manager-calendars-splits", "data-manager-calendars-squawk", "data-manager-chart", "data-manager-chart-config", "data-manager-chat", "data-manager-commodities", "data-manager-content", "data-manager-crypto", "data-manager-etfs", "data-manager-events", "data-manager-forex-manager", "data-manager-fund", "data-manager-geo", "data-manager-gov-trades", "data-manager-heatmap", "data-manager-holdings", "data-manager-iap", "data-manager-insider-trades", "data-manager-internal-news", "data-manager-layout", "data-manager-legacy-scanner", "data-manager-livestream", "data-manager-logo-analytics", "data-manager-marketing-wins", "data-manager-mortgage-rates", "data-manager-movers", "data-manager-news-alerts", "data-manager-notes", "data-manager-notification", "data-manager-onboarding", "data-manager-permission", "data-manager-plus", "data-manager-price-alerts", "data-manager-product", "data-manager-quote-holdings", "data-manager-quotes", "data-manager-scanner", "data-manager-scanner-config", "data-manager-securities", "data-manager-shop", "data-manager-signals", "data-manager-squawk", "data-manager-stock-reports", "data-manager-subscription", "data-manager-text-to-speech", "data-manager-time", "data-manager-token", "data-manager-top-stocks-manager", "data-manager-trade-ideas", "data-manager-trending-topics", "data-manager-user", "data-manager-videos", "data-manager-watchlists", "data-manager-watchlists-holdings", "data-manager-widget-linking", "data-manager-wiims", "data-manager-zing-rewards", "data-managers-examples", "data-session", "data-squawk-sdk", "google-sheets-db", "icons-fontawesome-custom", "icons-icons", "icons-v<PERSON><PERSON><PERSON>", "iter", "legacy-fission", "news-user-settings", "price-alert", "pro-e2e", "pro-user-settings", "react-utils-data-hooks-calendar-manager", "react-utils-data-hooks-chart-manager", "react-utils-data-hooks-content-manager", "react-utils-data-hooks-crypto-manager", "react-utils-data-hooks-fund-manager", "react-utils-data-hooks-livestream-manager", "react-utils-data-hooks-news-manager", "react-utils-data-hooks-quotes-manager", "react-utils-data-hooks-scanner-manager", "react-utils-data-hooks-securities-manager", "react-utils-data-hooks-time-manager", "react-utils-data-hooks-watchlist-manager", "react-utils-hooks", "react-utils-pro-tools", "react-utils-session-context", "react-utils-themetron", "react-utils-user-context", "react-utils-widget-tools", "search-modules", "styles-global-styles", "third-party-chartiq", "tsmd", "ui-ads", "ui-ads-utils", "ui-ag-grid-utils", "ui-alternative-investments", "ui-article", "ui-atomics", "ui-auth", "ui-blocks", "ui-bz-onboarding", "ui-calculators", "ui-calendars", "ui-charts", "ui-comments", "ui-core", "ui-crypto", "ui-edge", "ui-entity", "ui-etfs", "ui-filter", "ui-forms", "ui-image", "ui-logos", "ui-miller-columns", "ui-money", "ui-navigation", "ui-news", "ui-next-utils", "ui-pro-ui", "ui-product", "ui-quotes", "ui-reviews", "ui-search", "ui-styles", "ui-table", "ui-templates", "ui-testing", "ui-ticker", "ui-trade-ideas", "ui-translate", "ui-ui", "ui-user", "ui-watchlist-ui", "ui-widgets", "user-settings", "utils-analytics", "utils-auth", "utils-blocks", "utils-containers", "utils-date", "utils-device", "utils-frontend", "utils-manager-desktop-notification", "utils-manager-play-sound", "utils-manager-tracking", "utils-monitoring-coralogix", "utils-monitoring-datadog", "utils-safe-await", "utils-seo", "utils-subscribable", "utils-utils", "visualization-analyst-ratings", "visualization-bar-chart", "visualization-guage", "visualization-heatmap", "visualization-iqchart", "visualization-pie-chart", "visualization-plotly", "visualization-sank<PERSON>", "visualization-trading-view-charting-libarary", "visualization-trading-view-light-weight-chart", "visualization-visualization-utils", "widget-chartwidget", "widget-partner-widget", "widget-pro-bz-chart", "widget-pro-calendars", "widget-pro-chart", "widget-pro-chat", "widget-pro-details", "widget-pro-gpt", "widget-pro-graph", "widget-pro-home", "widget-pro-insiders", "widget-pro-movers", "widget-pro-newsfeed", "widget-pro-notification", "widget-pro-research", "widget-pro-scanner", "widget-pro-signals", "widget-pro-watchlist", "widget-pro-widget-utils", "widget-scanner", "widget-sensa-market", "widget-ticker-finder", "widget-utils", "widgets-pro-calendar", "widgets-pro-insiders"], "type": "bug", "updatedAt": "2024-08-14T15:18:26.183Z"}