{"affectedProjects": ["bz", "bz-mobile", "india", "money", "newsdesk-tools", "pro", "proto", "widgets-pro-calendar", "widgets-pro-insiders", "@benzinga/root", "amp", "bz-e2e", "data-manager-advanced-news", "data-manager-alts", "data-manager-article", "data-manager-basic-news", "data-manager-content", "data-manager-internal-news", "data-manager-news-alerts", "data-managers-examples", "legacy-fission", "news-user-settings", "pro-e2e", "react-utils-data-hooks-calendar-manager", "react-utils-data-hooks-content-manager", "react-utils-data-hooks-news-manager", "react-utils-data-hooks-quotes-manager", "react-utils-data-hooks-watchlist-manager", "react-utils-pro-tools", "react-utils-user-context", "react-utils-widget-tools", "search-modules", "signals-user-settings", "ui-ads", "ui-ads-utils", "ui-alternative-investments", "ui-article", "ui-atomics", "ui-auth", "ui-blocks", "ui-bz-onboarding", "ui-calculators", "ui-calendars", "ui-charts", "ui-comments", "ui-core", "ui-crypto", "ui-edge", "ui-entity", "ui-filter", "ui-forms", "ui-identity", "ui-miller-columns", "ui-money", "ui-navigation", "ui-news", "ui-pro-ui", "ui-product", "ui-quotes", "ui-reviews", "ui-search", "ui-table", "ui-templates", "ui-ticker", "ui-trade-ideas", "ui-ui", "ui-user", "ui-watchlist-ui", "ui-widgets", "utils-analytics", "utils-blocks", "visualization-analyst-ratings", "visualization-bar-chart", "visualization-guage", "visualization-heatmap", "visualization-iqchart", "visualization-pie-chart", "visualization-plotly", "visualization-sank<PERSON>", "visualization-trading-view-charting-libarary", "visualization-visualization-utils", "widget-chartwidget", "widget-pro-bz-chart", "widget-pro-calendars", "widget-pro-chart", "widget-pro-chat", "widget-pro-details", "widget-pro-gpt", "widget-pro-graph", "widget-pro-insiders", "widget-pro-movers", "widget-pro-newsfeed", "widget-pro-notification", "widget-pro-research", "widget-pro-scanner", "widget-pro-signals", "widget-pro-watchlist", "widget-pro-widget-utils", "widget-scanner", "widget-sensa-market", "widget-ticker-finder"], "description": "add verticals holding modules to other widgets in pro", "epic": null, "issueNumber": "11908", "project": "PRO", "projects": ["bz-mobile", "india", "money", "proto", "widgets-pro-calendar", "widgets-pro-insiders", "@benzinga/root", "amp", "bz-e2e", "data-manager-advanced-news", "data-manager-alts", "data-manager-article", "data-manager-basic-news", "data-manager-content", "data-manager-internal-news", "data-manager-news-alerts", "data-managers-examples", "legacy-fission", "news-user-settings", "pro-e2e", "react-utils-data-hooks-calendar-manager", "react-utils-data-hooks-content-manager", "react-utils-data-hooks-news-manager", "react-utils-data-hooks-quotes-manager", "react-utils-data-hooks-watchlist-manager", "react-utils-pro-tools", "react-utils-user-context", "react-utils-widget-tools", "search-modules", "signals-user-settings", "ui-ads", "ui-ads-utils", "ui-alternative-investments", "ui-article", "ui-atomics", "ui-auth", "ui-blocks", "ui-bz-onboarding", "ui-calculators", "ui-calendars", "ui-charts", "ui-comments", "ui-core", "ui-crypto", "ui-edge", "ui-entity", "ui-filter", "ui-forms", "ui-identity", "ui-miller-columns", "ui-money", "ui-navigation", "ui-news", "ui-pro-ui", "ui-product", "ui-quotes", "ui-reviews", "ui-search", "ui-table", "ui-templates", "ui-ticker", "ui-trade-ideas", "ui-ui", "ui-user", "ui-watchlist-ui", "ui-widgets", "utils-analytics", "utils-blocks", "visualization-analyst-ratings", "visualization-bar-chart", "visualization-guage", "visualization-heatmap", "visualization-iqchart", "visualization-pie-chart", "visualization-plotly", "visualization-sank<PERSON>", "visualization-trading-view-charting-libarary", "visualization-visualization-utils", "widget-chartwidget", "widget-pro-bz-chart", "widget-pro-calendars", "widget-pro-chart", "widget-pro-chat", "widget-pro-details", "widget-pro-gpt", "widget-pro-graph", "widget-pro-insiders", "widget-pro-movers", "widget-pro-newsfeed", "widget-pro-notification", "widget-pro-research", "widget-pro-scanner", "widget-pro-signals", "widget-pro-watchlist", "widget-pro-widget-utils", "widget-scanner", "widget-sensa-market", "widget-ticker-finder"], "type": "task", "updatedAt": "2024-09-04T12:48:57.255Z"}