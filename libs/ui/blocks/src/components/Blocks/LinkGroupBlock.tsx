import React from 'react';
import styled from '@benzinga/themetron';
import { css, keyframes } from 'styled-components';
import { sanitizeHTML } from '@benzinga/frontend-utils';
import { SimpleNewsQueryAndOptions } from '@benzinga/internal-news-manager';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowUpRightFromSquare } from '@fortawesome/pro-solid-svg-icons';
import { appEnvironment, appName } from '@benzinga/utils';

export interface LinkGroupBlockProps {
  attrs: {
    data: {
      action?: string;
      links: Link[];
      open_in_new_tab?: boolean;
      query?: SimpleNewsQueryAndOptions;
      title: string;
      variant?: 'default' | 'secondary';
      highlightLatest?: boolean;
      maxLinesLimit?: number;
    };
  };
}

export interface Link {
  title: string;
  url: string;
  created?: string;
  list_icon?: string;
}

export const LinkGroupBlock: React.FC<LinkGroupBlockProps> = ({ attrs }) => {
  if (!attrs.data) return null;

  const links = attrs.data?.links ?? [];
  const title = attrs.data?.title || 'Links';
  const variant = attrs.data?.variant || 'default';
  const maxLinesLimit = attrs.data?.maxLinesLimit || 3;
  const isIndiaApp = appEnvironment().isApp(appName.india);

  const Container = variant === 'secondary' ? SecondaryContainer : PrimaryContainer;

  return (
    <Container className="link-group-container" maxLinesLimit={maxLinesLimit}>
      <div className="link-group-title-wrapper">
        <h3>{title}</h3>
        {isIndiaApp && (
          <span className="see-more">
            <a href="https://in.benzinga.com/news">
              <FontAwesomeIcon color="rgb(36, 52, 79)" fontSize={20} fontWeight={600} icon={faArrowUpRightFromSquare} />
            </a>
          </span>
        )}
      </div>
      <ul className="link-group-list">
        {links.map((link, index) => (
          <li key={link.title}>
            {variant !== 'default' && link.created && (
              <Time isHighlighted={isHighlighted(index, timeAgo(link.created))}>{timeAgo(link.created)}</Time>
            )}
            {variant !== 'default' && link.created && (
              <Icon isHighlighted={isHighlighted(index, timeAgo(link.created))} />
            )}
            <a
              dangerouslySetInnerHTML={{ __html: sanitizeHTML(link.title) }}
              data-action={`${title} Link Group Click`}
              href={link.url}
            />
          </li>
        ))}
      </ul>
    </Container>
  );
};

const timeAgo = (dateString: string): string => {
  const date: Date = new Date(dateString);
  const now: Date = new Date();
  const diff: number = Math.abs(now.getTime() - date.getTime());

  const minutes: number = Math.floor(diff / (1000 * 60));
  const hours: number = Math.floor(minutes / 60);

  if (minutes < 60) {
    return `${minutes}m`;
  } else {
    return `${hours}h`;
  }
};

const isHighlighted = (index: number, dateString: string): boolean => {
  return index > 0 ? false : dateString.endsWith('m');
};

const PrimaryContainer = styled.div`
  border: 3px solid #192a34;
  background-color: #e5efff;

  .link-group-title-wrapper {
    background-color: #192a34;
    padding: 10px;
    margin: 0;
    h3 {
      color: #fff;
      font-size: ${({ theme }) => theme.fontSize.lg};
    }
  }

  .link-group-list {
    list-style-type: none;
    padding: 20px;
    margin: 0;

    li {
      position: relative;
      padding-left: 25px;
      font-size: ${({ theme }) => theme.fontSize.base};

      a {
        color: unset;
      }

      &:hover {
        text-decoration: underline;
      }

      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 6px;
        width: 6px;
        height: 6px;
        background-color: #192a34;
        border-radius: 50%;
      }

      &:not(:last-of-type) {
        margin-bottom: 4px;
      }
    }
  }
`;

const SecondaryContainer = styled.div<{ maxLinesLimit?: number }>`
  margin: 2em auto;

  .link-group-title-wrapper {
    justify-content: space-between;
    display: flex;
    h3 {
      color: var(--Light-Blue-60---25, #192940);
      font-size: 26px;
      font-style: normal;
      font-weight: 700;
      line-height: 30px;
      margin-left: 5px;
    }
    .see-more {
      margin-top: 5px;
      margin-right: 20px;
    }
  }

  .link-group-list {
    list-style-type: none;
    padding-top: 20px;
    padding-left: 5px;
    margin: 0;

    li {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      a {
        display: inline-block;
        max-width: calc(100% - 57px);
        color: black;
        vertical-align: middle;
        white-space: normal;
        overflow: hidden;
        text-overflow: ellipsis;
        max-height: ${({ maxLinesLimit }) => (maxLinesLimit ? `${maxLinesLimit * 1.5}em` : 'unset')};
        line-height: 1.5em;
        margin-left: 10px;
        font-weight: 600;
        font-size: smaller;
      }
    }
  }
`;

const blinkAnimation = keyframes`
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
`;

const Icon = styled.div<{ isHighlighted: boolean }>`
  width: 11px;
  height: 11px;
  border-radius: 8px;
  border: 3px solid ${({ isHighlighted }) => (isHighlighted ? '#ecd9d9' : 'var(--Light-Blue-10---98, #e1ebfa)')};
  background: ${({ isHighlighted }) => (isHighlighted ? '#ac2f2f' : 'var(--Blue-100, #3f83f8)')};
  margin-top: 8px;
  align-self: flex-start;

  ${({ isHighlighted }) =>
    isHighlighted &&
    css`
      animation: ${blinkAnimation} 1.5s infinite;
    `}
`;

const Time = styled.div<{ isHighlighted: boolean }>`
  width: 39px;
  height: 18px;
  color: ${({ isHighlighted }) => (isHighlighted ? '#ac2f2f' : '5b7292')};
  font-size: 12px;
  font-family: Manrope;
  font-weight: 800;
  line-height: 20px;
  padding-left: 6px;
  margin-top: 4px;
  align-self: flex-start;
  border-radius: 8px 0px 0px 8px;
  background: linear-gradient(
    90deg,
    ${({ isHighlighted }) => (isHighlighted ? '#ecd9d9' : '#e1ebfa')} 44%,
    rgba(225, 235, 250, 0) 100%
  );
`;
// margin: 1em auto;

// &.widget-container {
//   border: 3px solid #192a34;
//   background-color: #e5efff;
//   font-family: sans-serif;

//   .widget-title {
//     background-color: #192a34;
//     color: #fff;
//     padding: 10px;
//     margin: 0;
//   }

//   .widget-bulleted-list {
//     list-style-type: none;
//     padding: 20px;
//     margin: 0;
//   }

//   .widget-bulleted-list li {
//     position: relative;
//     padding-left: 20px;
//     margin-bottom: 10px;
//     font-size: ${({ theme }) => theme.fontSize.base};

//     &:hover {
//       text-decoration: underline;
//     }

//     a {
//       color: unset;
//     }
//   }

//   .widget-bulleted-list li::before {
//     content: '';
//     position: absolute;
//     left: 0;
//     top: 5px;
//     width: 8px;
//     height: 8px;
//     background-color: #192a34;
//     border-radius: 50%;
//   }
// }
