'use client';
import React from 'react';
import { NextPage } from 'next';
import styled from '@benzinga/themetron';
import Hooks from '@benzinga/hooks';
import { TrackingManager } from '@benzinga/tracking-manager';
import { SessionContext } from '@benzinga/session-context';

export type CustomErrorProps = {
  statusCode: number;
  title?: string;
  subtitle?: string;
};

const ContentDefault: React.FC<{ statusCode: number; text?: string }> = ({ statusCode, text }) => {
  return (
    <>
      <p>{pageSubtitle(statusCode, text)}</p>
      <div>
        <a href="/">
          <span>Go to Home</span>
        </a>
      </div>
    </>
  );
};

const ContentNotFound: React.FC = () => {
  return (
    <>
      <PageNotFoundImage />
      <h1>Oops! the page you were looking for could not be found</h1>
      <p>This page doesn't exist or was removed. We suggest you head back to home</p>
      <a href="/">
        <span>Go to Home</span>
      </a>
    </>
  );
};

const pageTitle = (statusCode: number, title?: string) => {
  if (title) {
    return title;
  }
  return `Uh-Oh! ${statusCode}`;
};

const pageSubtitle = (statusCode: number, subtitle?: string) => {
  if (subtitle) {
    return subtitle;
  }
  if (statusCode === 410) {
    return 'The requested resource is no longer available and has been removed permanently.';
  }
  return 'Oops! Something went wrong. Please try again soon <NAME_EMAIL>.';
};

const CustomError: NextPage<CustomErrorProps> = ({ statusCode, subtitle, title }) => {
  const session = React.useContext(SessionContext);
  const trackError = statusCode => {
    if (statusCode === 404) {
      session.getManager(TrackingManager).trackErrorEvent('emit', {
        error_message: '404 Page Not Found',
      });
    } else if (statusCode === 410) {
      session.getManager(TrackingManager).trackErrorEvent('emit', {
        error_message: '410 Page Gone',
      });
    }
  };

  Hooks.useEffectDidMount(() => {
    trackError(statusCode);
  });

  return (
    <StyledCustomError className={statusCode === 404 ? 'block-404' : 'block-error'}>
      <div>
        {statusCode === 404 ? null : <h1>{pageTitle(statusCode, title)}</h1>}
        {statusCode === 404 ? <ContentNotFound /> : <ContentDefault statusCode={statusCode} text={subtitle} />}
      </div>
    </StyledCustomError>
  );
};

CustomError.getInitialProps = async ({ err, res }): Promise<CustomErrorProps> => {
  const statusCode = res ? res.statusCode : err?.statusCode ? err.statusCode : 404;
  return { statusCode };
};

export default CustomError;

const StyledCustomError = styled.div`
  &.block-404,
  &.block-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: calc(100vh - 132px);
    background-color: ${({ theme }) => theme.colorPalette.gray50};
    mix-blend-mode: normal;
    padding: 10px;
    text-align: center;

    h1 {
      font-size: ${({ theme }) => theme.fontSize['3xl']};
      color: ${({ theme }) => theme.colorPalette.gray800};
      font-weight: ${({ theme }) => theme.fontWeight.semibold};
      text-transform: uppercase;
      margin-top: 20px;
    }

    p {
      font-size: ${({ theme }) => theme.fontSize.lg};
      color: ${({ theme }) => theme.colorPalette.gray400};
      margin-top: 10px;
      margin-bottom: 20px;
    }

    > div {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }

    .page-not-found-image {
      display: inline-grid;
      max-width: 450px;
      width: 100%;
    }

    a {
      color: ${({ theme }) => theme.colorPalette.white};
      background-color: ${({ theme }) => theme.colorPalette.blue500};
      font-weight: ${({ theme }) => theme.fontWeight.semibold};
      width: 160px;
      height: 50px;
      border-radius: ${({ theme }) => theme.borderRadius.default};
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
  }
`;

const PageNotFoundImage = () => {
  return (
    <span className="page-not-found-image">
      <img alt="404 Space Image" src="/next-assets/images/404-space.svg" />
    </span>
  );
};
